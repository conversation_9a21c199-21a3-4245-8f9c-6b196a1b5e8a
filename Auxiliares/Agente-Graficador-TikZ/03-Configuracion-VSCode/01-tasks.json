{"version": "2.0.0", "tasks": [{"label": "🎨 Agente TikZ + Augment: <PERSON><PERSON><PERSON>", "type": "shell", "command": "python3", "args": ["${workspaceFolder}/agente_imagen.py", "${file}"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "detail": "Analiza imagen seleccionada con Augment IA y genera código TikZ profesional"}, {"label": "🎨 Agente TikZ: <PERSON><PERSON><PERSON><PERSON> Personalizado", "type": "shell", "command": "python3", "args": ["${workspaceFolder}/agente_imagen.py", "${file}", "${input:prompt<PERSON><PERSON><PERSON><PERSON><PERSON>}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Analiza imagen con prompt personalizado para Augment IA"}, {"label": "📋 Compilar TikZ con LaTeX", "type": "shell", "command": "pdflatex", "args": ["-interaction=nonstopmode", "${file}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": ["$latex"], "detail": "Compila archivo TikZ/LaTeX seleccionado"}, {"label": "🔍 Ver Resultado TikZ", "type": "shell", "command": "ls", "args": ["-la", "tikz_generado/"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "detail": "Mostrar archivos generados en tikz_generado/"}], "inputs": [{"id": "prompt<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Prompt personalizado para análisis con Augment IA", "default": "Analiza esta imagen matemática con especial atención a las funciones y elementos geométricos", "type": "promptString"}]}